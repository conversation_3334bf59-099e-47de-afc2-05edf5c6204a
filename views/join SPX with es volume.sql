-- Join SPX data with ES volume data
-- This script combines SPX index data with ES futures volume data for analysis

-- Example query to join SPX and ES volume data
-- Customize based on your specific database schema and requirements

SELECT 
    s.[date],
    s.[close] AS spx_close,
    e.[total_volume] AS es_volume,
    e.[avg_close] AS es_avg_close
FROM SPX s
LEFT JOIN (
    SELECT 
        DATEADD(SECOND, (DATEDIFF(SECOND, '2000-01-01', date) / 10) * 10, '2000-01-01') AS time_bucket,
        SUM([volume]) AS total_volume,
        AVG([close]) AS avg_close
    FROM ES_data
    GROUP BY DATEADD(SECOND, (DATEDIFF(SECOND, '2000-01-01', date) / 10) * 10, '2000-01-01')
) e ON s.[date] = e.time_bucket
WHERE s.[date] >= '2024-01-01'
ORDER BY s.[date];

-- Add any additional join logic here
-- This is a placeholder - customize based on your requirements 