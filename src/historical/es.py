from ibapi.client import EClient
from ibapi.wrapper import EWrapper
from ibapi.contract import Contract
from ibapi.ticktype import TickTypeEnum
from datetime import datetime, timedelta
from dateutil import tz
from src.db.database import get_db

import json
import os
import pandas as pd
import pyodbc
import sys
import time
import threading



class IB_His_ES(EWrapper, EClient):
    def __init__(self):
        EClient.__init__(self, self)
        self.dataReceived = threading.Event()
        self.dataList = []


    def error(self, reqId, errorCode, errorString):
        if errorCode in [2104, 2106, 2158]:
            return
        print(f"ERROR {reqId} {errorCode}: {errorString}")


    def historicalData(self, reqId: int, bar):
        # print(f"Req {reqId} | {bar.date} | O: {bar.open}, H: {bar.high}, L: {bar.low}, C: {bar.close}, V: {bar.volume}")
        self.dataList.append(bar)


    def historicalDataEnd(self, reqId: int, start: str, end: str):
        print(f"*** Request {reqId} complete. Start: {TimeFormatter().ESTime(datetime.strptime(start, '%Y%m%d %H:%M:%S'))}, End: {TimeFormatter().ESTime(datetime.strptime(end, '%Y%m%d %H:%M:%S'))}. ***")
        self.dataReceived.set()


class _Create_Contract(object):
    def ES(self, expDate):
        self.contract = Contract()

        self.contract.symbol = "ES"
        self.contract.secType = "FUT"    # CONTFUT
        self.contract.exchange = "CME"
        self.contract.currency = "USD"
        self.contract.lastTradeDateOrContractMonth = expDate

        return self.contract


class DBQuery(object):
    def dbExec(self, symbol, dataList):
        self.valueList = [(
            symbol,
            datetime.strptime(value.date, '%Y%m%d %H:%M:%S').strftime('%Y-%m-%d %H:%M:%S'),
            value.open, value.high, value.low, value.close, value.volume
        ) for value in dataList]

        es_Data = pd.DataFrame(self.valueList, columns = ['Symbol', 'Date', 'Open', 'High', 'Low', 'Close', 'Volume'])
        get_db().bulk_insert_dataframe(symbol, es_Data)


class TimeFormatter(object):
    def UTCTime(self, date):
        return date.replace(tzinfo = tz.gettz("America/New_York")).astimezone(tz.UTC)


    def ESTime(self, date):
        return date.replace(tzinfo = tz.UTC).astimezone(tz.gettz("America/New_York")).strftime('%Y-%m-%d %H:%M:%S')


    def TimePeriod(self, start, end):
        if end == '':
            end = datetime.now(tz = tz.UTC)
        else:
            end = self.UTCTime(datetime.strptime(end, '%Y-%m-%d %H:%M:%S'))

        return int((end - start).total_seconds())


class Main(object):
    def __init__(self):
        self.expDate = '202509'
        self.ES_Contract = _Create_Contract().ES(self.expDate)

        self.startDT = '2025-06-14 00:00:00'
        self.endDT = '2025-07-30 23:59:59'    # Set endDT = '' to fetch until present.
        # self.endDT = ''

        self.startSpot = TimeFormatter().UTCTime(datetime.strptime(self.startDT, '%Y-%m-%d %H:%M:%S'))
        self.timePeriod = TimeFormatter().TimePeriod(self.startSpot, self.endDT)
        self.execRounds = self.timePeriod // 1800 + 1

        print(f'''*** Preparing to fetch Historical data of {self.ES_Contract.symbol},
        Start: {self.startDT}, End: {self.endDT},
        Time Period: {self.timePeriod} seconds, Execution Rounds: {self.execRounds}. ***''')


    def fetchHistorical(self):
        # ''' Subscripition ES historical data:
        #     ES historical data request, request Id: 7(70). '''
        his_ES = IB_His_ES()
        his_ES.connect('127.0.0.1', 7497, 7)

        es_Thread = threading.Thread(target = his_ES.run, daemon = True)
        es_Thread.start()
        time.sleep(1)

        self.totalBars = 0

        for i in range(self.execRounds):
            his_ES.dataReceived.clear()
            his_ES.dataList.clear()

            self.timeStep = f"{(self.startSpot + timedelta(minutes = 30 * (i + 1))).strftime('%Y%m%d-%H:%M:%S')}"

            his_ES.reqHistoricalData(
                i + 1,    # Request ID.
                self.ES_Contract,
                endDateTime = self.timeStep,    # Set to empty when secType = 'CONTFUT'.
                durationStr = '1800 S',
                barSizeSetting = '1 secs',
                whatToShow = 'Trades',
                useRTH = False,
                formatDate = 1,    # 2 is timestamp.
                keepUpToDate = False,
                chartOptions = []
            )

            his_ES.dataReceived.wait(timeout = 10)
            self.totalBars += len(his_ES.dataList)

            DBQuery().dbExec(self.ES_Contract.symbol, his_ES.dataList)

            # API restriction:
            # Pacing Violations for Small Bars (30 secs or less).
            # Making identical historical data requests within 15 seconds.
            # Making six or more historical data requests for the same Contract, Exchange and Tick Type within 2 seconds.
            # Making more than 60 requests within any 10 minute period.
            time.sleep(11.5)

        print(f"*** Total 1-sec bars collected from {self.startDT} to {self.endDT}: {self.totalBars}. ***")
        his_ES.disconnect()



if __name__ == "__main__":
    Main().fetchHistorical()