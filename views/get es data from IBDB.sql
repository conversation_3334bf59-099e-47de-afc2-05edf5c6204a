-- Get ES data from IBDB
-- This script retrieves ES (E-mini S&P 500) data from the IBKR database

-- Example query to get ES data
-- Customize based on your specific database schema and requirements

SELECT 
    [date],
    [open],
    [high],
    [low],
    [close],
    [volume]
FROM [IBDB].[dbo].[ES_data]
WHERE date >= '2024-01-01'
ORDER BY date;

-- Add any additional ES data retrieval logic here
-- This is a placeholder - customize based on your requirements 